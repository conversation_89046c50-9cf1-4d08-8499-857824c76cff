import '@testing-library/jest-dom';

import { fireEvent, render, screen } from '@testing-library/react';

import StatusCompletionModal from './StatusCompletionModal';

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  usePathname: () => '/jobs',
}));

// Mock the jobs store
jest.mock('@/stores/unifiedJobStore', () => ({
  useJobsStore: () => ({
    refreshJobs: jest.fn(),
    invalidateJobsCache: jest.fn(),
  }),
}));

// Mock window.location
const mockLocation = {
  href: '',
};
Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true,
});

describe('StatusCompletionModal', () => {
  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
    title: 'Test Title',
    description: 'Test Description',
    status: 'success' as const,
    jobId: 'test-job-guid-12345', // Use a GUID-like format to represent actual job entity ID
    targetPath: '/jobs',
    actionType: 'completed' as const,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockLocation.href = '';
  });

  describe('View Result Button', () => {
    it('should show View Result button for successful matchRank completion', () => {
      render(<StatusCompletionModal {...defaultProps} action="matchRank" />);

      expect(screen.getByText('View Result')).toBeInTheDocument();
      expect(screen.queryByText('Refresh')).not.toBeInTheDocument();
    });

    it('should show View Result button for successful upload completion', () => {
      render(<StatusCompletionModal {...defaultProps} action="upload" />);

      expect(screen.getByText('View Result')).toBeInTheDocument();
      expect(screen.queryByText('Refresh')).not.toBeInTheDocument();
    });

    it('should show View Result button for successful scout completion', () => {
      render(<StatusCompletionModal {...defaultProps} action="scout" />);

      expect(screen.getByText('View Result')).toBeInTheDocument();
      expect(screen.queryByText('Refresh')).not.toBeInTheDocument();
    });

    it('should navigate to ranked mode for matchRank action', () => {
      render(<StatusCompletionModal {...defaultProps} action="matchRank" />);

      fireEvent.click(screen.getByText('View Result'));

      expect(mockLocation.href).toBe('/jobs/test-job-guid-12345/candidates');
    });

    it('should navigate to edit mode for upload action', () => {
      render(<StatusCompletionModal {...defaultProps} action="upload" />);

      fireEvent.click(screen.getByText('View Result'));

      expect(mockLocation.href).toBe('/jobs/test-job-guid-12345/edit');
    });

    it('should navigate to edit mode for scout action', () => {
      render(<StatusCompletionModal {...defaultProps} action="scout" />);

      fireEvent.click(screen.getByText('View Result'));

      expect(mockLocation.href).toBe('/jobs/test-job-guid-12345/edit');
    });
  });

  describe('Refresh Button', () => {
    it('should show Refresh button for error status', () => {
      render(<StatusCompletionModal {...defaultProps} status="error" action="matchRank" />);

      expect(screen.getByText('Refresh')).toBeInTheDocument();
      expect(screen.queryByText('View Result')).not.toBeInTheDocument();
    });

    it('should show Refresh button when no jobId provided', () => {
      render(<StatusCompletionModal {...defaultProps} jobId={undefined} action="matchRank" />);

      expect(screen.getByText('Refresh')).toBeInTheDocument();
      expect(screen.queryByText('View Result')).not.toBeInTheDocument();
    });

    it('should show Refresh button when no targetPath provided', () => {
      render(<StatusCompletionModal {...defaultProps} targetPath={undefined} action="matchRank" />);

      expect(screen.getByText('Refresh')).toBeInTheDocument();
      expect(screen.queryByText('View Result')).not.toBeInTheDocument();
    });

    it('should show Refresh button for non-completed actionType', () => {
      render(<StatusCompletionModal {...defaultProps} actionType="failed" action="matchRank" />);

      expect(screen.getByText('Refresh')).toBeInTheDocument();
      expect(screen.queryByText('View Result')).not.toBeInTheDocument();
    });
  });

  describe('Button Functionality', () => {
    it('should call onClose when View Result button is clicked', () => {
      const mockOnClose = jest.fn();
      render(<StatusCompletionModal {...defaultProps} onClose={mockOnClose} action="matchRank" />);

      fireEvent.click(screen.getByText('View Result'));
      expect(mockOnClose).toHaveBeenCalled();
    });

    it('should call onClose when Refresh button is clicked', () => {
      const mockOnClose = jest.fn();
      render(
        <StatusCompletionModal
          {...defaultProps}
          onClose={mockOnClose}
          status="error"
          action="matchRank"
        />
      );

      fireEvent.click(screen.getByText('Refresh'));
      expect(mockOnClose).toHaveBeenCalled();
    });
  });
});
