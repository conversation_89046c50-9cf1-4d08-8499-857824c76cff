import React from 'react';
import {
  Building,
  MapPin,
  DollarSign,
  Calendar,
  Users,
  Clock,
  Briefcase,
  LayoutList,
  ListChecks,
  BadgeCheck,
  ShieldCheck,
  BookOpen,
  GraduationCap,
  Languages,
  Lightbulb,
  HeartHandshake,
  TrendingUp,
  Globe,
  Mail,
  Star,
  Rocket,
} from 'lucide-react';
import { IJob } from '@/entities/interfaces';
import { motion } from 'framer-motion';

interface JobDetailsTabProps {
  job: IJob;
}

const SectionCard: React.FC<{ children: React.ReactNode; delay?: number; noPadding?: boolean }> = ({
  children,
  delay = 0,
  noPadding = false,
}) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5, delay }}
    className={`bg-gradient-to-br from-white/[0.01] to-transparent backdrop-blur-sm rounded-xl border border-white/[0.03] hover:border-white/[0.05] transition-all duration-300 ${noPadding ? '' : 'p-6'}`}
  >
    {children}
  </motion.div>
);

export const JobDetailsTab: React.FC<JobDetailsTabProps> = ({ job }) => {
  return (
    <div className="w-full">
      {/* Hero Header Section */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="relative overflow-hidden mb-8 -mt-8 -mx-8"
      >
        {/* Background with image and overlay */}
        <div className="relative h-[280px] overflow-hidden">
          {/* Background Image */}
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `url('/images/insights/team_collaboration_revised.png')`,
              backgroundPosition: 'center 20%',
              backgroundSize: 'cover',
              backgroundRepeat: 'no-repeat',
            }}
          />

          {/* Gradient overlay - transparent at top, darker at bottom */}
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-purple-900/50 to-purple-900/80" />
          <div className="absolute inset-0 bg-gradient-to-t from-gray-900/70 via-transparent to-transparent" />

          {/* Content */}
          <div className="relative h-full flex items-center px-8 lg:px-12">
            <div className="max-w-7xl mx-auto w-full">
              <div className="flex items-start gap-6">
                {/* Company Avatar */}
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
                  className="flex-shrink-0 hidden sm:block"
                >
                  <div className="w-28 h-28 rounded-2xl bg-white/20 backdrop-blur-md border-2 border-white/30 flex items-center justify-center text-4xl font-bold text-white shadow-2xl hover:scale-105 transition-transform duration-300">
                    {job.companyName?.charAt(0).toUpperCase() || 'C'}
                  </div>
                </motion.div>

                {/* Job Info */}
                <div className="flex-1 text-white">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 }}
                    className="flex items-center gap-4 mb-3"
                  >
                    <h1 className="text-3xl lg:text-4xl xl:text-5xl font-bold">
                      {job.jobType || job.jobTitle || 'Untitled Position'}
                    </h1>

                    {/* Live/Draft Status Chip */}
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.5, type: 'spring', stiffness: 200 }}
                      className={`
                        flex items-center gap-2 px-4 py-2 rounded-full text-sm font-semibold
                        ${
                          (job as any)?.isPublished
                            ? 'bg-green-500/20 text-green-400 border border-green-400/30'
                            : 'bg-orange-500/20 text-orange-400 border border-orange-400/30'
                        }
                        backdrop-blur-sm shadow-lg
                      `}
                    >
                      <Rocket className="w-4 h-4" />
                      <span>{(job as any)?.isPublished ? 'Live' : 'Draft'}</span>
                    </motion.div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4 }}
                    className="flex flex-wrap items-center gap-4 text-white/90 mb-6"
                  >
                    <div className="flex items-center gap-2">
                      <Building className="w-5 h-5" />
                      <span className="font-medium text-lg">
                        {job.companyName || 'Company Name'}
                      </span>
                    </div>
                    {job.location && (
                      <div className="flex items-center gap-2">
                        <MapPin className="w-5 h-5" />
                        <span>{Array.isArray(job.location) ? job.location[0] : job.location}</span>
                      </div>
                    )}
                  </motion.div>

                  {/* Quick Stats */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    className="flex flex-wrap gap-4"
                  >
                    {job.salaryRange && (
                      <div className="flex items-center gap-2">
                        <DollarSign className="w-4 h-4 text-green-300" />
                        <span className="font-medium text-white/90">{job.salaryRange}</span>
                      </div>
                    )}
                    {job.experience && (
                      <div className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-yellow-300" />
                        <span className="font-medium text-white/90">
                          {job.experience.charAt(0).toUpperCase() +
                            job.experience.slice(1).toLowerCase()}{' '}
                          Level
                        </span>
                      </div>
                    )}
                    {job.typeOfJob && (
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-blue-300" />
                        <span className="font-medium text-white/90">
                          {job.typeOfJob
                            .toLowerCase()
                            .split('_')
                            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                            .join(' ')}
                        </span>
                      </div>
                    )}
                  </motion.div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-8 pb-8 space-y-6">
        {/* Key Details - No Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="flex flex-wrap gap-6 pb-6 mb-6 border-b border-white/[0.05]"
        >
          {job.typeOfHiring && (
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-500/10 rounded-lg">
                <Users className="w-4 h-4 text-blue-400" />
              </div>
              <div>
                <p className="text-xs text-gray-500 uppercase tracking-wider">Hiring Type</p>
                <p className="font-medium text-white">
                  {job.typeOfHiring
                    .toLowerCase()
                    .split('_')
                    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                    .join(' ')}
                </p>
              </div>
            </div>
          )}

          {job.department && (
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-500/10 rounded-lg">
                <Briefcase className="w-4 h-4 text-purple-400" />
              </div>
              <div>
                <p className="text-xs text-gray-500 uppercase tracking-wider">Department</p>
                <p className="font-medium text-white">
                  {job.department
                    .split(' ')
                    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                    .join(' ')}
                </p>
              </div>
            </div>
          )}

          {job.createdAt && (
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-500/10 rounded-lg">
                <Calendar className="w-4 h-4 text-green-400" />
              </div>
              <div>
                <p className="text-xs text-gray-500 uppercase tracking-wider">Posted</p>
                <p className="font-medium text-white">
                  {new Date(job.createdAt).toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric',
                  })}
                </p>
              </div>
            </div>
          )}

          {job.experience && (
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-500/10 rounded-lg">
                <Star className="w-4 h-4 text-orange-400" />
              </div>
              <div>
                <p className="text-xs text-gray-500 uppercase tracking-wider">Experience</p>
                <p className="font-medium text-white">
                  {job.experience.charAt(0).toUpperCase() + job.experience.slice(1).toLowerCase()}{' '}
                  Level
                </p>
              </div>
            </div>
          )}

          {job.typeOfJob && (
            <div className="flex items-center gap-3">
              <div className="p-2 bg-cyan-500/10 rounded-lg">
                <Clock className="w-4 h-4 text-cyan-400" />
              </div>
              <div>
                <p className="text-xs text-gray-500 uppercase tracking-wider">Type</p>
                <p className="font-medium text-white">
                  {job.typeOfJob
                    .toLowerCase()
                    .split('_')
                    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                    .join(' ')}
                </p>
              </div>
            </div>
          )}
        </motion.div>

        {/* Company Description */}
        {job?.companyDescription && (
          <SectionCard delay={0.4}>
            <h3 className="text-xl font-semibold mb-4 flex items-center gap-3 text-white">
              <div className="p-2 bg-purple-500/20 rounded-lg">
                <Building className="w-5 h-5 text-purple-400" />
              </div>
              About {job.companyName || 'the Company'}
            </h3>
            <p className="text-gray-300 leading-relaxed">{job.companyDescription}</p>
          </SectionCard>
        )}

        {/* Responsibilities */}
        {job?.jobResponsibilities && job.jobResponsibilities.length > 0 && (
          <SectionCard delay={0.5}>
            <h3 className="text-xl font-semibold mb-4 flex items-center gap-3 text-white">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <LayoutList className="w-5 h-5 text-blue-400" />
              </div>
              Key Responsibilities
            </h3>
            <div className="space-y-2">
              {job.jobResponsibilities.map((responsibility: string, index: number) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.6 + index * 0.1 }}
                  className="flex items-start gap-3 group pl-2"
                >
                  <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0 group-hover:scale-125 transition-transform" />
                  <span className="text-gray-300 flex-1 leading-relaxed">
                    {responsibility?.charAt(0).toUpperCase() + responsibility?.slice(1)}
                  </span>
                </motion.div>
              ))}
            </div>
          </SectionCard>
        )}

        {/* Skills Section */}
        {job?.skills && job.skills.length > 0 && (
          <SectionCard delay={0.6}>
            <h3 className="text-xl font-semibold mb-4 flex items-center gap-3 text-white">
              <div className="p-2 bg-green-500/20 rounded-lg">
                <BadgeCheck className="w-5 h-5 text-green-400" />
              </div>
              Required Skills
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2">
              {job.skills.map((skill: string, index: number) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.7 + index * 0.05 }}
                  className="flex items-start gap-3 group"
                >
                  <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 flex-shrink-0 group-hover:scale-125 transition-transform" />
                  <span className="text-gray-300 leading-relaxed">{skill}</span>
                </motion.div>
              ))}
            </div>
          </SectionCard>
        )}

        {/* Education & Languages */}
        {((job?.education && job.education.length > 0) ||
          (job?.language && job.language.length > 0)) && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {job?.education && job.education.length > 0 && (
              <SectionCard delay={0.7}>
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-3 text-white">
                  <div className="p-2 bg-orange-500/20 rounded-lg">
                    <GraduationCap className="w-5 h-5 text-orange-400" />
                  </div>
                  Education Requirements
                </h3>
                <ul className="space-y-2">
                  {job.education.map((edu: string, index: number) => (
                    <motion.li
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.75 + index * 0.05 }}
                      className="flex items-start gap-3 text-gray-300"
                    >
                      <div className="w-1.5 h-1.5 bg-orange-400 rounded-full mt-2 flex-shrink-0" />
                      <span className="leading-relaxed">{edu}</span>
                    </motion.li>
                  ))}
                </ul>
              </SectionCard>
            )}

            {job?.language && job.language.length > 0 && (
              <SectionCard delay={0.8}>
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-3 text-white">
                  <div className="p-2 bg-cyan-500/20 rounded-lg">
                    <Languages className="w-5 h-5 text-cyan-400" />
                  </div>
                  Language Requirements
                </h3>
                <ul className="space-y-2">
                  {job.language.map((lang: string, index: number) => (
                    <motion.li
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.85 + index * 0.05 }}
                      className="flex items-start gap-3 text-gray-300"
                    >
                      <div className="w-1.5 h-1.5 bg-cyan-400 rounded-full mt-2 flex-shrink-0" />
                      <span className="leading-relaxed">{lang}</span>
                    </motion.li>
                  ))}
                </ul>
              </SectionCard>
            )}
          </div>
        )}

        {/* Soft Skills */}
        {job?.softSkills && job.softSkills.length > 0 && (
          <SectionCard delay={0.9}>
            <h3 className="text-xl font-semibold mb-4 flex items-center gap-3 text-white">
              <div className="p-2 bg-yellow-500/20 rounded-lg">
                <Lightbulb className="w-5 h-5 text-yellow-400" />
              </div>
              Soft Skills
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2">
              {job.softSkills.map((skill: string, index: number) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.95 + index * 0.05 }}
                  className="flex items-start gap-3 group"
                >
                  <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2 flex-shrink-0 group-hover:scale-125 transition-transform" />
                  <span className="text-gray-300 leading-relaxed">{skill}</span>
                </motion.div>
              ))}
            </div>
          </SectionCard>
        )}

        {/* Benefits & Career Growth */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {job?.benefits && job.benefits.length > 0 && (
            <SectionCard delay={1.0}>
              <h3 className="text-xl font-semibold mb-4 flex items-center gap-3 text-white">
                <div className="p-2 bg-pink-500/20 rounded-lg">
                  <HeartHandshake className="w-5 h-5 text-pink-400" />
                </div>
                Benefits Package
              </h3>
              <ul className="space-y-2">
                {job.benefits.map((benefit: string, index: number) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 1.05 + index * 0.05 }}
                    className="flex items-start gap-3 text-gray-300"
                  >
                    <div className="w-1.5 h-1.5 bg-pink-400 rounded-full mt-2 flex-shrink-0" />
                    <span className="leading-relaxed">{benefit}</span>
                  </motion.li>
                ))}
              </ul>
            </SectionCard>
          )}

          {job?.careerGrowth && job.careerGrowth.length > 0 && (
            <SectionCard delay={1.1}>
              <h3 className="text-xl font-semibold mb-4 flex items-center gap-3 text-white">
                <div className="p-2 bg-indigo-500/20 rounded-lg">
                  <TrendingUp className="w-5 h-5 text-indigo-400" />
                </div>
                Career Growth Opportunities
              </h3>
              <ul className="space-y-2">
                {job.careerGrowth.map((item: string, index: number) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 1.15 + index * 0.05 }}
                    className="flex items-start gap-3 text-gray-300"
                  >
                    <div className="w-1.5 h-1.5 bg-indigo-400 rounded-full mt-2 flex-shrink-0" />
                    <span className="leading-relaxed">{item}</span>
                  </motion.li>
                ))}
              </ul>
            </SectionCard>
          )}
        </div>
      </div>
    </div>
  );
};
