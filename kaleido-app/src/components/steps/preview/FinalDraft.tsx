'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';

import { FileText, MessageSquare, PenTool, Save, Sparkles } from 'lucide-react';
import { useRouter } from 'next/navigation';

import Separator from '@/components/common/Separator';
import { showToast } from '@/components/Toaster';
import { useJobs } from '@/contexts/jobs/JobsContext';
import { ExperienceLevel, JobStatus } from '@/entities/Job.entities';
import apiHelper from '@/lib/apiHelper';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { Box } from '@mui/material';
import StepLayout from '../jd-creations/layout/StepLayout';

import EditableContent from '../EditableContent';
import GeneratedJD from './GeneratedJD';
import PreviewContent from './PreviewContent';
import ToneSelector from './ToneSelector';

interface SaveStatus {
  loading: boolean;
  error: string | null;
  success: boolean;
}

interface GenerationStatus {
  isLoading: boolean;
  error: string | null;
}

const FinalDraft: React.FC = () => {
  const { job, updateJobDescription, clearJobData } = useJobs();
  const { addJobFromDescription } = useJobsStore();
  const [isEditable, setIsEditable] = useState(false);
  const [step, setStep] = useState<number>(0);
  const [saveStatus, setSaveStatus] = useState<SaveStatus>({
    loading: false,
    error: null,
    success: false,
  });
  const router = useRouter();
  const [windowWidth, setWindowWidth] = useState(
    typeof window !== 'undefined' ? window.innerWidth : 1024
  );

  const [selectedTone, setSelectedTone] = useState<string>('professional');
  const [generatedJD, setGeneratedJD] = useState<string | null>(null);
  const [generationStatus, setGenerationStatus] = useState<GenerationStatus>({
    isLoading: false,
    error: null,
  });

  const initialSaveComplete = useRef(false);
  const isMounted = useRef(false);

  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [jobSaved, setJobSaved] = useState<boolean>(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const stepParam = urlParams.get('step');
    const jobId = urlParams.get('jobId');
    const isEdit = urlParams.get('edit') === 'true';
    const jobDescriptionStorage = localStorage.getItem('job');

    // If we're in edit mode with jobId, wait for the job data to be loaded
    if (jobId && isEdit) {
      // The job data will be loaded by JobsContext
      setStep(Number(stepParam) || 1);
      return;
    }

    if (!stepParam || !jobDescriptionStorage || jobDescriptionStorage === '') {
      setStep(1);
      localStorage.setItem('step', '1');

      // If no job data in localStorage, create a minimal job object
      if (!jobDescriptionStorage || jobDescriptionStorage === '') {
        const minimalJob = {
          jobTitle: 'New Job',
          jobType: '',
          companyName: '',
          department: '',
          skills: [],
          jobResponsibilities: [],
          experience: '',
          experienceLevel: ExperienceLevel.MID,
          isGraduateRole: false,
          status: JobStatus.NEW,
          location: [],
          benefits: [],
          requirements: [],
          topCandidateThreshold: 0,
          secondTierCandidateThreshold: 0,
        };
        localStorage.setItem('job', JSON.stringify(minimalJob));
      }
    } else {
      setStep(Number(stepParam));
    }
  }, []);

  useEffect(() => {
    const storedData = localStorage.getItem('job');
    if (storedData) {
      const jobData = JSON.parse(storedData);
      if (jobData.generatedJD) {
        setGeneratedJD(jobData.generatedJD);
      }
      if (jobData.generatedJDTone) {
        setSelectedTone(jobData.generatedJDTone);
      }
      // Check if job is saved (has an ID)
      setJobSaved(!!jobData.id);
    }
  }, []);

  // Monitor job save status
  useEffect(() => {
    const checkJobSaveStatus = () => {
      const storedJob = localStorage.getItem('job');
      if (storedJob) {
        try {
          const jobData = JSON.parse(storedJob);
          const hasId = !!jobData.id;
          // Only update if the value has changed
          setJobSaved(prev => {
            if (prev !== hasId) {
              return hasId;
            }
            return prev;
          });
        } catch (error) {
          setJobSaved(false);
        }
      } else {
        setJobSaved(false);
      }
    };

    // Check initially
    checkJobSaveStatus();

    // Poll for changes with a longer interval
    const interval = setInterval(checkJobSaveStatus, 1000);

    return () => clearInterval(interval);
  }, []);

  const saveToDatabase = useCallback(async (forcesSave = false) => {
    if (initialSaveComplete.current && !forcesSave) {
      return false;
    }

    setSaveStatus({ loading: true, error: null, success: false });

    try {
      // Get job data from localStorage or use current job from context as fallback
      const storedData = localStorage.getItem('job');
      let jobData: any;

      if (!storedData) {
        // If no data in localStorage, use the job from context
        if (Object.keys(job).length === 0) {
          // If job context is also empty, create a minimal job object
          jobData = {
            jobTitle: 'New Job',
            jobType: '',
            companyName: '',
            department: '',
            skills: [],
            jobResponsibilities: [],
            experience: '',
            experienceLevel: ExperienceLevel.MID,
            isGraduateRole: false,
            status: JobStatus.NEW,
            location: [],
            benefits: [],
            requirements: [],
            topCandidateThreshold: 0,
            secondTierCandidateThreshold: 0,
          };
          // Save this minimal job to localStorage for future use
          localStorage.setItem('job', JSON.stringify(jobData));
        } else {
          // Use job from context
          jobData = { ...job };
        }
      } else {
        // Parse the stored data
        jobData = JSON.parse(storedData);
      }

      // Check if we're editing an existing job
      const urlParams = new URLSearchParams(window.location.search);
      const urlJobId = urlParams.get('jobId');
      const isEditFromUrl = urlParams.get('edit') === 'true';

      const isEditingJob =
        (jobData.isEditing && jobData.editingJobId) || (urlJobId && isEditFromUrl);
      const jobId = jobData.id || jobData.editingJobId || urlJobId;

      // Check if we already have a job ID - if so, update instead of create
      if (jobId) {
        // Remove editing flags and system fields from the data before saving
        const {
          isEditing,
          editingJobId,
          createdAt,
          updatedAt,
          matchedCandidatesCount,
          candidatesCount,
          clientId,
          ...cleanJobData
        } = jobData;

        const formattedJobData = {
          ...cleanJobData,
          id: jobId, // Ensure we use the correct ID
          status: cleanJobData.status || JobStatus.NEW,
          skills: Array.isArray(cleanJobData.skills) ? cleanJobData.skills : [],
          jobResponsibilities: Array.isArray(cleanJobData.jobResponsibilities)
            ? cleanJobData.jobResponsibilities
            : [],
          companyValues: Array.isArray(cleanJobData.companyValues)
            ? cleanJobData.companyValues
            : [],
          culturalFit: Array.isArray(cleanJobData.culturalFit) ? cleanJobData.culturalFit : [],
          education: Array.isArray(cleanJobData.education) ? cleanJobData.education : [],
          language: Array.isArray(cleanJobData.language) ? cleanJobData.language : [],
          softSkills: Array.isArray(cleanJobData.softSkills) ? cleanJobData.softSkills : [],
          location: Array.isArray(cleanJobData.location) ? cleanJobData.location : [],
          benefits: Array.isArray(cleanJobData.benefits) ? cleanJobData.benefits : [],
          careerGrowth: Array.isArray(cleanJobData.careerGrowth) ? cleanJobData.careerGrowth : [],
          candidates: Array.isArray(cleanJobData.candidates) ? cleanJobData.candidates : [],
          cultureFitQuestions: Array.isArray(cleanJobData.cultureFitQuestions)
            ? cleanJobData.cultureFitQuestions
            : [],
        };

        // Update existing job
        await useJobsStore.getState().updateJob(jobId, formattedJobData);
        initialSaveComplete.current = true;

        setSaveStatus({
          loading: false,
          error: null,
          success: true,
        });

        showToast({ message: 'Successfully updated job description' });
        return true;
      } else {
        // Create new job if no ID exists
        // Remove empty id, clientId and other fields that shouldn't be sent for new job creation
        const {
          id,
          clientId,
          createdAt,
          updatedAt,
          matchedCandidatesCount,
          candidatesCount,
          ...cleanJobData
        } = jobData;

        const formattedJobData = {
          ...cleanJobData,
          status: cleanJobData.status || JobStatus.NEW,
          skills: Array.isArray(cleanJobData.skills) ? cleanJobData.skills : [],
          jobResponsibilities: Array.isArray(cleanJobData.jobResponsibilities)
            ? cleanJobData.jobResponsibilities
            : [],
          companyValues: Array.isArray(cleanJobData.companyValues)
            ? cleanJobData.companyValues
            : [],
          culturalFit: Array.isArray(cleanJobData.culturalFit) ? cleanJobData.culturalFit : [],
          education: Array.isArray(cleanJobData.education) ? cleanJobData.education : [],
          language: Array.isArray(cleanJobData.language) ? cleanJobData.language : [],
          softSkills: Array.isArray(cleanJobData.softSkills) ? cleanJobData.softSkills : [],
          location: Array.isArray(cleanJobData.location) ? cleanJobData.location : [],
          benefits: Array.isArray(cleanJobData.benefits) ? cleanJobData.benefits : [],
          careerGrowth: Array.isArray(cleanJobData.careerGrowth) ? cleanJobData.careerGrowth : [],
          candidates: Array.isArray(cleanJobData.candidates) ? cleanJobData.candidates : [],
          cultureFitQuestions: Array.isArray(cleanJobData.cultureFitQuestions)
            ? cleanJobData.cultureFitQuestions
            : [],
        };

        const job = await addJobFromDescription(formattedJobData);

        if (job && job.id) {
          // Update the job ID in localStorage to ensure it's available for the Success component
          const updatedJobData = {
            ...jobData,
            id: job.id,
          };
          localStorage.setItem('job', JSON.stringify(updatedJobData));

          // Clear the editing flags after successful save
          localStorage.removeItem('isEditingJob');

          initialSaveComplete.current = true;

          // Trigger job saved state update
          setJobSaved(true);
        }

        setSaveStatus({
          loading: false,
          error: null,
          success: true,
        });

        showToast({ message: 'Successfully saved job description' });
        return true;
      }
    } catch (error) {
      console.error('Error saving job description:', error);
      setSaveStatus({
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to save job description',
        success: false,
      });
      return false;
    }
  }, []);

  useEffect(() => {
    if (!isMounted.current) {
      isMounted.current = true;
      // Only save if we have a job that hasn't been saved yet
      const storedJob = localStorage.getItem('job');
      if (storedJob) {
        try {
          const jobData = JSON.parse(storedJob);
          // Only save if the job doesn't have an ID yet
          if (!jobData.id) {
            saveToDatabase();
          }
        } catch (error) {
          console.error('Error parsing stored job:', error);
        }
      }
    }
  }, []); // Empty dependency array to run only once on mount

  const handleEdit = async () => {
    if (isEditable) {
      setIsSaving(true);
      setSaveStatus({ loading: true, error: null, success: false });

      try {
        await saveToDatabase();
        setSaveStatus({ loading: false, error: null, success: true });
        setIsEditable(false);
      } catch (error) {
        setSaveStatus({
          loading: false,
          error: error instanceof Error ? error.message : 'Failed to save changes',
          success: false,
        });
      } finally {
        setIsSaving(false);
      }
    } else {
      setIsEditable(true);
    }
  };

  const handleGenerateJD = async () => {
    if (!selectedTone) return;

    try {
      setGenerationStatus({ isLoading: true, error: null });

      // Get the job ID from our job context first, fallback to localStorage
      let jobId = job.id;

      if (!jobId) {
        const storedJob = localStorage.getItem('job');
        if (storedJob) {
          const parsedJob = JSON.parse(storedJob);
          // Check for editingJobId or URL jobId as well
          const urlParams = new URLSearchParams(window.location.search);
          const urlJobId = urlParams.get('jobId');
          jobId = parsedJob.id || parsedJob.editingJobId || urlJobId;
        }
      }

      // If still no job ID, save the job first
      if (!jobId) {
        showToast({ message: 'Saving job before generating description...' });
        const saveSuccess = await saveToDatabase(true);

        if (!saveSuccess) {
          setGenerationStatus({
            isLoading: false,
            error: 'Failed to save job. Please try again.',
          });
          return;
        }

        // After saving, get the job ID again
        jobId = job.id;
        if (!jobId) {
          const storedJob = localStorage.getItem('job');
          if (storedJob) {
            const parsedJob = JSON.parse(storedJob);
            jobId = parsedJob.id;
          }
        }

        // If still no job ID after saving, there's an issue
        if (!jobId) {
          setGenerationStatus({
            isLoading: false,
            error: 'Failed to save job. Please try again.',
          });
          return;
        }
      }

      const response = await apiHelper.post('/jobs/generate-tone', {
        jobId: jobId,
        tone: selectedTone,
      });

      if (response.generatedJD) {
        setGeneratedJD(response.generatedJD);
        updateJobDescription('generatedJD', response.generatedJD);
        updateJobDescription('generatedJDTone', selectedTone);
        showToast({ message: 'Job description generated successfully' });
        setIsEditing(true); // Set editing mode after generation
      } else {
        setGenerationStatus({
          isLoading: false,
          error: 'Failed to generate job description. Please try again.',
        });
      }
    } catch (error) {
      setGenerationStatus({
        isLoading: false,
        error: 'An error occurred while generating the job description.',
      });
      showToast({ message: 'Failed to generate job description', isSuccess: false });
    } finally {
      setGenerationStatus(prev => ({ ...prev, isLoading: false }));
    }
  };

  useEffect(() => {
    const prevPath = window.location.pathname;
    const prevStep = new URLSearchParams(window.location.search).get('step');

    return () => {
      const currentPath = window.location.pathname;
      const currentStep = new URLSearchParams(window.location.search).get('step');

      if (
        prevPath === '/job-description-creation' &&
        prevStep === 'final-draft' &&
        (currentPath !== prevPath || currentStep !== prevStep)
      ) {
        // clearJobData();
      }
    };
  }, [clearJobData]);

  return (
    <StepLayout
      title="Final Draft"
      description="You can make changes before publishing."
      icon={FileText}
    >
      {isEditable ? (
        <div className="relative h-full">
          <EditableContent isEditable={true} />
          <button
            type="button"
            onClick={handleEdit}
            disabled={saveStatus.loading}
            className={`absolute top-4 right-4 p-2 rounded-full bg-blue-500 hover:bg-blue-600 transition-colors ${
              saveStatus.loading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            title="Save changes"
          >
            <Save size={18} className="text-white" />
          </button>
        </div>
      ) : (
        <Box
          className="flex flex-col lg:flex-row gap-0 h-full"
          sx={{
            height: '100%',
            width: '100%',
          }}
        >
          {/* Final Draft Section */}
          <Box className="relative flex-1 overflow-hidden w-full lg:w-1/2 flex flex-col">
            <button
              type="button"
              onClick={handleEdit}
              className="absolute top-4 right-4 p-2 rounded-full bg-gray-700/50 hover:bg-gray-600/70 transition-colors z-10"
              title="Edit draft"
            >
              <PenTool size={18} className="text-white" />
            </button>

            {/* Sticky header */}
            <div className="sticky top-0 z-10 px-4 py-2 border-b border-gray-700/30">
              <h3 className="text-lg font-semibold">Final Draft</h3>
            </div>

            {/* Scrollable content */}
            <div className="flex-grow overflow-y-auto">
              <PreviewContent />
            </div>
          </Box>

          {/* Separator */}
          <Box className="flex">
            <Separator
              orientation={windowWidth >= 1024 ? 'vertical' : 'horizontal'}
              className={windowWidth >= 1024 ? '' : ''}
            />
          </Box>

          {/* Generated JD Section */}
          <Box className="relative flex-1 overflow-hidden w-full lg:w-1/2 flex flex-col">
            {/* Sticky header with tone selector */}
            <div className="sticky top-0 z-10 px-4 py-2 border-b border-gray-700/30">
              <h3 className="text-lg font-semibold mb-2">Generated JD</h3>

              {/* Tone selector */}
              <div className="mb-2">
                <div className="flex items-center mb-2">
                  <MessageSquare size={16} className="mr-2 text-gray-400" />
                  <span className="text-sm font-medium">Job Description Tone</span>
                </div>
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                  <div className="flex-grow w-full sm:w-auto">
                    <ToneSelector
                      selectedTone={selectedTone}
                      onChange={setSelectedTone}
                      disabled={generationStatus.isLoading}
                    />
                  </div>
                  <button
                    onClick={handleGenerateJD}
                    disabled={generationStatus.isLoading || !jobSaved}
                    className="flex items-center gap-2 px-4 py-2 rounded-lg bg-gradient-to-r from-purple-600 via-pink-600 to-purple-700 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 text-white  hover:shadow-xl hover:shadow-purple-500/25 border border-purple-400/10 hover:border-purple-300/50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Sparkles className="w-4 h-4" />
                    {generationStatus.isLoading ? 'Generating...' : 'Generate'}
                  </button>
                </div>
              </div>
            </div>

            {/* Scrollable content - aligned to top */}
            <div className="flex-grow overflow-y-auto h-full flex flex-col">
              <div className="flex-grow">
                <GeneratedJD
                  generatedJD={generatedJD}
                  isLoading={generationStatus.isLoading}
                  error={generationStatus.error}
                />
              </div>
            </div>
          </Box>
        </Box>
      )}
    </StepLayout>
  );
};

export default FinalDraft;
