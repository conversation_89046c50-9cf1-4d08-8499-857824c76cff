import React, { useCallback, useEffect, useState } from 'react';

import debounce from 'lodash/debounce';

import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import TextEditor from '@/components/TextEditor';
import { Box, Typography } from '@mui/material';

interface GeneratedJDProps {
  generatedJD: string | null;
  isLoading: boolean;
  error: string | null;
  onSave?: (content: string) => Promise<void>;
  onTextChange?: (content: string) => void;
  disabled?: boolean;
}

const GeneratedJD: React.FC<GeneratedJDProps> = ({
  generatedJD,
  isLoading,
  error,
  onSave,
  onTextChange,
  disabled = false,
}) => {
  const [editedJD, setEditedJD] = useState<string>(generatedJD || '');
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [saveError, setSaveError] = useState<string | null>(null);

  // Update local state when generatedJD changes
  useEffect(() => {
    if (generatedJD) {
      setEditedJD(generatedJD);
    }
  }, [generatedJD]);

  // Auto-save with debounce
  const debouncedSave = useCallback(
    debounce(async (content: string) => {
      if (!onSave) return;

      try {
        setIsSaving(true);
        setSaveError(null);
        await onSave(content);
      } catch (error) {
        setSaveError(error instanceof Error ? error.message : 'Failed to save job description');
      } finally {
        setIsSaving(false);
      }
    }, 1000),
    [onSave]
  );

  // Cleanup debounce on unmount
  useEffect(() => {
    return () => {
      debouncedSave.cancel();
    };
  }, [debouncedSave]);

  const handleChange = (content: string) => {
    setEditedJD(content);

    // Notify parent component about text changes
    if (onTextChange) {
      onTextChange(content);
    }

    // Auto-save if enabled
    if (onSave) {
      debouncedSave(content);
    }
  };

  if (isLoading) {
    return (
      <Box className="flex flex-col items-center justify-center h-full">
        <ColorfulSmokeyOrbLoader text="Generating job description" useModalBg={false} />
      </Box>
    );
  }

  if (error) {
    return (
      <Box className="p-4">
        <Box className="p-4 bg-red-100 bg-opacity-10 rounded-md">
          <Typography color="error" className="mb-2">
            Error generating job description
          </Typography>
          <Typography variant="body2" className="text-gray-400">
            {error}
          </Typography>
        </Box>
      </Box>
    );
  }

  if (!generatedJD && !editedJD) {
    return (
      <Box className="p-4">
        <Box className="p-4 bg-gray-100 bg-opacity-10 rounded-md">
          <Typography variant="body1" className="text-gray-400">
            {disabled
              ? 'Please wait for the job to be saved before generating a job description.'
              : 'Select a tone above and click "Generate" to create a job description with your preferred tone.'}
          </Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box className="flex flex-col w-full h-full p-4">
      <Box
        className="flex-grow rounded-md overflow-y-auto"
        style={{
          height: '100%',
          backgroundColor: 'rgba(30, 30, 40, 0.4)',
          boxShadow: 'inset 0 2px 10px rgba(0, 0, 0, 0.15)',
          padding: '16px',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <TextEditor
          value={editedJD}
          onChange={handleChange}
          height="100%"
          placeholder="Your job description will appear here"
          isEditable={!isSaving && !disabled}
          noBorder={true}
          textColor="white"
        />

        {saveError && (
          <Typography color="error" variant="body2" className="mt-2">
            {saveError}
          </Typography>
        )}

        {isSaving && (
          <Typography variant="body2" className="mt-2 text-gray-400">
            Saving changes...
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export default GeneratedJD;
