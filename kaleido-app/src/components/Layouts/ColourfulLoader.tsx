import React, { useEffect, useState } from 'react';

import { motion } from 'framer-motion';
import Image from 'next/image';

interface ColorfulSmokeyOrbLoaderProps {
  text?: string;
  useModalBg?: boolean;
}

// Particle component for smoky orbs
const SmokyParticle = ({
  color,
  size,
  delay,
  duration,
  startPosition,
}: {
  color: string;
  size: number;
  delay: number;
  duration: number;
  startPosition: { x: string; y: string };
}) => {
  const pathVariants = {
    initial: {
      x: startPosition.x,
      y: startPosition.y,
      opacity: 0,
      scale: 0.3,
    },
    animate: {
      x: `calc(${startPosition.x} + ${Math.random() > 0.5 ? '' : '-'}${Math.random() * 100}px)`,
      y: `calc(${startPosition.y} - ${30 + Math.random() * 100}px)`,
      opacity: [0, 0.4, 0],
      scale: [0.3, 0.6 + Math.random() * 0.5, 0.2],
      transition: {
        duration: duration,
        delay: delay,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  };

  // We need to use inline styles for dynamic size and position values
  // that can't be represented with Tailwind classes
  return (
    <motion.div
      className={`absolute rounded-full blur-md ${color}`}
      style={{
        width: size,
        height: size,
        left: startPosition.x,
        top: startPosition.y,
      }}
      variants={pathVariants}
      initial="initial"
      animate="animate"
    />
  );
};

const ColorfulSmokeyOrbLoader: React.FC<ColorfulSmokeyOrbLoaderProps> = ({
  text = 'Loading',
  useModalBg = true,
}) => {
  const [particles, setParticles] = useState<React.ReactNode[]>([]);

  // Generate particles on component mount
  useEffect(() => {
    const colors = [
      'bg-purple-500/40',
      'bg-pink-500/40',
      'bg-blue-500/40',
      'bg-indigo-500/40',
      'bg-teal-500/40',
      'bg-orange-500/40',
    ];

    const newParticles = Array.from({ length: 15 }).map((_, i) => {
      const size = 10 + Math.random() * 30;
      const delay = Math.random() * 2;
      const duration = 3 + Math.random() * 4;
      const angle = Math.random() * Math.PI * 2;
      const distance = 30 + Math.random() * 30;
      const x = `calc(50% + ${Math.cos(angle) * distance}px)`;
      const y = `calc(50% + ${Math.sin(angle) * distance}px)`;

      return (
        <SmokyParticle
          key={i}
          color={colors[i % colors.length]}
          size={size}
          delay={delay}
          duration={duration}
          startPosition={{ x, y }}
        />
      );
    });

    setParticles(newParticles);
  }, []);

  // Logo shape animations
  const shapeVariants = {
    initial: { scale: 0.8, opacity: 0 },
    animate: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  // Floating animation for shapes
  const floatAnimation = {
    animate: {
      y: [0, -8, 0],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  };

  // Pulse animation for glow effect
  const pulseAnimation = {
    animate: {
      scale: [1, 1.05, 1],
      opacity: [0.7, 0.9, 0.7],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  };

  // Rotation animation for the outer ring
  const rotateAnimation = {
    animate: {
      rotate: [0, 360],
      transition: {
        duration: 20,
        repeat: Infinity,
        ease: 'linear',
      },
    },
  };

  // Text animation
  const textContainer = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
        duration: 0.2,
      },
    },
  };

  const textItem = {
    hidden: { opacity: 0, y: 5 },
    show: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.2,
      },
    },
  };

  // Dots animation
  const dots = {
    animate: {
      opacity: [0.4, 1, 0.4],
      transition: {
        duration: 1.2,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  };

  return (
    <motion.div
      className={`fixed inset-0 flex flex-col items-center justify-center z-50 ${useModalBg ? 'bg-black/10 backdrop-blur-sm' : ''}`}
      role="status"
      aria-label="Loading"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
    >
      {/* Logo animation container */}
      <div className="relative w-40 h-40 mb-4">
        {/* Smoky particles */}
        <div className="absolute inset-0">{particles}</div>

        {/* Rotating outer ring */}
        <motion.div
          className="absolute inset-0 rounded-full border border-white/10"
          variants={rotateAnimation}
          animate="animate"
        >
          <div className="absolute w-3 h-3 rounded-full bg-purple-500/80 blur-[1px] top-[2%] left-1/2 transform -translate-x-1/2" />
          <div className="absolute w-3 h-3 rounded-full bg-pink-500/80 blur-[1px] top-1/2 right-[2%] transform -translate-y-1/2" />
          <div className="absolute w-3 h-3 rounded-full bg-blue-500/80 blur-[1px] bottom-[2%] left-1/2 transform -translate-x-1/2" />
          <div className="absolute w-3 h-3 rounded-full bg-teal-500/80 blur-[1px] top-1/2 left-[2%] transform -translate-y-1/2" />
        </motion.div>

        {/* Background glow */}
        <motion.div
          className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500/30 to-pink-500/30 blur-xl"
          variants={pulseAnimation}
          animate="animate"
        />

        {/* Logo shapes */}
        <div className="relative w-full h-full flex items-center justify-center">
          {/* Use the actual logo image */}
          <motion.div
            className="absolute inset-0 flex items-center justify-center"
            initial="initial"
            animate="animate"
            variants={shapeVariants}
          >
            <Image
              src="/images/logos/kaleido-logo-only.webp"
              alt="Kaleido Logo"
              width={90}
              height={90}
              className="z-10"
              priority
              style={{
                width: 'auto',
                height: 'auto',
                maxWidth: '90px',
                maxHeight: '90px',
              }}
            />
          </motion.div>

          {/* Animated colored shapes */}
          <motion.div
            className="absolute w-6 h-6 rounded-full bg-gradient-to-r from-pink-500 to-purple-500 blur-[1px] left-[15%] top-[20%]"
            variants={floatAnimation}
            animate="animate"
          />
          <motion.div
            className="absolute w-4 h-4 rounded-full bg-gradient-to-r from-blue-500 to-teal-500 blur-[1px] left-[70%] top-[25%]"
            variants={floatAnimation}
            animate="animate"
            transition={{ delay: 0.5 }}
          />
          <motion.div
            className="absolute w-5 h-5 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 blur-[1px] left-[30%] top-[65%]"
            variants={floatAnimation}
            animate="animate"
            transition={{ delay: 1.0 }}
          />
          <motion.div
            className="absolute w-3 h-3 rounded-full bg-gradient-to-r from-orange-500 to-pink-500 blur-[1px] left-[75%] top-[70%]"
            variants={floatAnimation}
            animate="animate"
            transition={{ delay: 1.5 }}
          />
        </div>
      </div>

      {/* Text */}
      <motion.div
        variants={textContainer}
        initial="hidden"
        animate="show"
        className="flex flex-col items-center"
      >
        <motion.div className="flex items-center text-xs font-light text-white/90 mb-1">
          {text.split('').map((char, index) => (
            <motion.span key={index} variants={textItem} className="inline-block">
              {char === ' ' ? '\u00A0' : char}
            </motion.span>
          ))}
          <motion.span variants={dots} animate="animate">
            ...
          </motion.span>
        </motion.div>

        {/* Company name */}
        <motion.div
          className="text-xs font-medium text-white/80"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          kaleido talent
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default ColorfulSmokeyOrbLoader;
