/** @type {import('next').NextConfig} */

const { withSentryConfig } = require('@sentry/nextjs');

const nextConfig = {
  // Exclude test files from being treated as pages
  pageExtensions: ['tsx', 'ts', 'jsx', 'js'].filter(ext => !ext.includes('test')),

  // Packages to transpile
  transpilePackages: ['@auth0/nextjs-auth0', 'oauth4webapi'],

  // External packages for server components
  serverExternalPackages: ['typeorm', 'pg'],

  // Experimental features
  experimental: {
    // Use more CPUs if available
    cpus: 4,
  },

  // Compiler options
  compiler: {
    // Remove console logs in production
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn']
    } : false,
  },

  // Turbopack configuration (moved from experimental.turbo)
  turbopack: {
    resolveAlias: {
      // Resolve potential Next.js internal conflicts
      'next/dist/client/components/static-generation-async-storage.external': 'next/dist/client/components/static-generation-async-storage.external.js',
    },
  },

  // Configure caching for development to improve hot reloading
  onDemandEntries: {
    // period (in ms) where the server will keep pages in the buffer
    maxInactiveAge: 25 * 1000,
    // number of pages that should be kept simultaneously without being disposed
    pagesBufferLength: 5,
  },

  // Optimize module imports
  modularizeImports: {
    '@mui/icons-material': {
      transform: '@mui/icons-material/{{member}}',
    },
    '@mui/material': {
      transform: '@mui/material/{{member}}',
    },
    'lodash': {
      transform: 'lodash/{{member}}',
    },
  },

  // Disable source maps in production for faster builds
  productionBrowserSourceMaps: false,

  // Use standalone output for smaller deployments
  output: 'standalone',

  // Configure webpack for compatibility and optimization
  webpack: (config, { dev, isServer, webpack }) => {
    // Exclude test files from page compilation
    const originalEntry = config.entry;
    config.entry = async () => {
      const entries = await originalEntry();

      // Filter out test files from entries
      Object.keys(entries).forEach(key => {
        if (key.includes('__tests__') || key.includes('.test.') || key.includes('.spec.')) {
          delete entries[key];
        }
      });

      return entries;
    };

    // Add caching for webpack in production
    if (!dev) {
      config.cache = {
        type: 'filesystem',
        buildDependencies: {
          config: [__filename],
        },
        cacheDirectory: require('path').resolve('.next/cache/webpack'),
      };

      // Optimize module ids
      config.optimization.moduleIds = 'deterministic';
      
      // Better chunk splitting
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          default: false,
          vendors: false,
          framework: {
            name: 'framework',
            chunks: 'all',
            test: /[\\/]node_modules[\\/](react|react-dom|scheduler|prop-types|use-subscription)[\\/]/,
            priority: 40,
            enforce: true,
          },
          lib: {
            test(module) {
              return module.size() > 160000 &&
                /node_modules[/\\]/.test(module.identifier());
            },
            name(module) {
              const hash = require('crypto')
                .createHash('sha256')
                .update(module.identifier())
                .digest('hex');
              return `lib-${hash.substring(0, 8)}`;
            },
            priority: 30,
            minChunks: 1,
            reuseExistingChunk: true,
          },
          commons: {
            name: 'commons',
            minChunks: 2,
            priority: 20,
          },
        },
        maxAsyncRequests: 30,
        maxInitialRequests: 30,
      };
    }

    // Handle web workers
    config.module.rules.push({
      test: /\.worker\.(js|ts)$/,
      use: {
        loader: 'worker-loader',
        options: {
          filename: 'static/[hash].worker.js',
          publicPath: '/_next/'
        }
      }
    });

    // Handle non-server configurations first
    if (!isServer) {
      config.output.globalObject = 'self';
      config.resolve.fallback = {
        ...config.resolve.fallback,
        dns: false,
        fs: false,
        net: false,
        tls: false,
        crypto: require.resolve('crypto-browserify'),
        stream: require.resolve('stream-browserify'),
        path: require.resolve('path-browserify'),
        os: require.resolve('os-browserify/browser'),
        zlib: require.resolve('browserify-zlib'),
        'pg-native': false,
        'react-native-sqlite-storage': false,
        '@sap/hana-client': false,
        mysql: false,
        '@sap/hana-client/extension/Stream': false,
        buffer: require.resolve('buffer/'),
      };

      // FFmpeg.wasm specific configurations
      config.resolve.alias = {
        ...config.resolve.alias,
        '@ffmpeg/ffmpeg': '@ffmpeg/ffmpeg/dist/esm',
        '@ffmpeg/util': '@ffmpeg/util/dist/esm',
      };
    }

    // Remove problematic loaders
    config.module.rules = config.module.rules.filter(
      rule => !(rule.test?.toString().includes('typeorm') || rule.test?.toString().includes('pg'))
    );

    // Add media handling
    config.module.rules.push({
      test: /\.(mp4|webm|ogg)$/,
      use: {
        loader: 'file-loader',
        options: {
          publicPath: '/_next',
          name: 'static/media/[name].[hash].[ext]',
        },
      },
    });

    // Node loader
    config.module.rules.push({
      test: /\.node$/,
      use: 'node-loader',
    });

    // Server-specific configurations
    if (isServer) {
      config.externals = [
        ...(Array.isArray(config.externals) ? config.externals : []),
        { typeorm: 'typeorm' },
        'pg',
      ];
    }

    // Enable experimental features
    config.experiments = {
      ...config.experiments,
      topLevelAwait: true,
      layers: true,
      asyncWebAssembly: true,
    };

    // Handle FFmpeg.wasm specific issues
    if (!isServer) {
      // Add rule for .wasm files
      config.module.rules.push({
        test: /\.wasm$/,
        type: 'webassembly/async',
      });

      // Handle dynamic imports for FFmpeg.wasm
      config.module.rules.push({
        test: /node_modules\/@ffmpeg\/ffmpeg\/dist\/esm\/classes\.js$/,
        use: {
          loader: 'string-replace-loader',
          options: {
            search: 'new Worker\\(new URL\\(classWorkerURL, import\\.meta\\.url\\)',
            replace: 'new Worker(classWorkerURL)',
            flags: 'g'
          }
        }
      });
    }

    return config;
  },

  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
      },
      {
        protocol: 'https',
        hostname: '*.googleusercontent.com',
      },
      {
        protocol: 'https',
        hostname: '*.auth0.com',
      },
      {
        protocol: 'https',
        hostname: 'randomuser.me',
      },
      {
        protocol: 'https',
        hostname: 'logo.clearbit.com',
      },
      // Wildcard patterns for Kaleido and Digital Ocean domains
      {
        protocol: 'https',
        hostname: '*kaleido*',
      },
      {
        protocol: 'https',
        hostname: '*.digitaloceanspaces.com',
      },
      {
        protocol: 'https',
        hostname: '*.cdn.digitaloceanspaces.com',
      },
      // Specific existing domains (keeping for backwards compatibility)
      {
        protocol: 'https',
        hostname: 'kaleido-spaces-dev.lon1.digitaloceanspaces.com',
      },
      {
        protocol: 'https',
        hostname: 'kaleido-prod-space.lon1.cdn.digitaloceanspaces.com',
      },
      {
        protocol: 'https',
        hostname: 'res.cloudinary.com',
      },
      {
        protocol: 'https',
        hostname: 'media.licdn.com',
      },
      {
        protocol: 'https',
        hostname: 'gravatar.com',
      },
      {
        protocol: 'https',
        hostname: 's.gravatar.com',
      },
      {
        protocol: 'https',
        hostname: 'img.icons8.com',
      },
      {
        protocol: 'https',
        hostname: 'cdn.jsdelivr.net',
      },
    ],
  },

  reactStrictMode: true,

  serverRuntimeConfig: {
    projectRoot: process.cwd(),
  },

  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Content-Security-Policy',
            value: "frame-src 'self' https://www.loom.com https://www.youtube.com https://player.vimeo.com https://youtube.com https://youtu.be;",
          },
        ],
      },
    ];
  },

  // Add API rewrites to proxy requests to the backend
  async rewrites() {
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL_BASE || 'http://localhost:8080/api';

    return [
      // Auth routes that should be handled by Next.js, not proxied
      {
        source: '/api/auth/:path*',
        destination: '/api/auth/:path*',
      },
      // Proxy specific public API endpoints
      {
        source: '/api/companies/profile/:slug',
        destination: `${apiBaseUrl}/companies/profile/:slug`,
      },
      {
        source: '/api/companies/by-user',
        destination: `${apiBaseUrl}/companies/by-user`,
      },
      {
        source: '/api/jobs/company/:companyId/public',
        destination: `${apiBaseUrl}/jobs/company/:companyId/public`,
      },
      {
        source: '/api/jobs/:slug/slug',
        destination: `${apiBaseUrl}/jobs/:slug/slug`,
      },
      // Generic proxy for all other API requests
      {
        source: '/api/:path*',
        destination: `${apiBaseUrl}/:path*`,
      },
    ];
  },

};

// Sentry configuration for production only
const sentryWebpackPluginOptions = {
  org: process.env.SENTRY_ORG,
  project: process.env.SENTRY_PROJECT,
  authToken: process.env.SENTRY_AUTH_TOKEN,
  silent: true,
  widenClientFileUpload: true,
  transpileClientSDK: true,
  tunnelRoute: "/monitoring",
  hideSourceMaps: true,
  disableLogger: true,
  automaticVercelMonitors: false,
};

console.log('Using Next.js 15 with optimized build configuration');

// Export with Sentry only in production with auth token
module.exports = process.env.NODE_ENV === 'production' && process.env.SENTRY_AUTH_TOKEN
  ? withSentryConfig(nextConfig, sentryWebpackPluginOptions)
  : nextConfig;
